'use strict';

const { Sequelize } = require('sequelize');

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('开始修复农场区块计算公式...');
    
    // 获取所有农场区块数据
    const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    console.log(`找到 ${farmPlots.length} 个农场区块需要更新`);
    
    // 辅助函数：格式化为3位小数
    function formatToThreeDecimals(value) {
      return parseFloat(value.toFixed(3));
    }
    
    // 计算基础产量：基础产量 × (1.5)^(等级-1)
    // 基础产量 = 1 × (2.0) ^ (编号 - 1)
    function calculateBaseProduction(level, plotNumber) {
      const plotBaseProduction = Math.pow(2.0, plotNumber - 1);
      const levelMultiplier = Math.pow(1.5, level - 1);
      return formatToThreeDecimals(plotBaseProduction * levelMultiplier);
    }
    
    // 计算生产速度：5 ÷ (1.05)^(等级-1)
    function calculateProductionSpeed(level) {
      const baseSpeed = 5.0;
      const speedMultiplier = Math.pow(1.05, level - 1);
      return formatToThreeDecimals(baseSpeed / speedMultiplier);
    }
    
    // 计算升级费用：200 × (1.5)^(等级-1)
    function calculateUpgradeCost(level) {
      const baseCost = 200;
      const costMultiplier = Math.pow(1.5, level - 1);
      return formatToThreeDecimals(baseCost * costMultiplier);
    }
    
    // 计算解锁费用：2000 × (2.0)^(编号-2)
    function calculateUnlockCost(plotNumber) {
      if (plotNumber === 1) return 0;
      const baseCost = 2000;
      const costMultiplier = Math.pow(2.0, plotNumber - 2);
      const result = baseCost * costMultiplier;
      // 添加上限检查，防止超出INTEGER.UNSIGNED的最大值
      const MAX_UNLOCK_COST = 4294967295;
      return Math.min(result, MAX_UNLOCK_COST);
    }
    
    // 批量更新农场区数据
    for (const plot of farmPlots) {
      let updates = {};
      let needsUpdate = false;
      
      // 重新计算产量（考虑牧场区编号的基础产量倍数）
      const newMilkProduction = plot.isUnlocked ? 
        calculateBaseProduction(plot.level, plot.plotNumber) : 0;
      
      if (Math.abs(plot.milkProduction - newMilkProduction) > 0.001) {
        updates.milkProduction = newMilkProduction;
        needsUpdate = true;
      }
      
      // 重新计算生产速度
      const newProductionSpeed = calculateProductionSpeed(plot.level);
      
      if (Math.abs(plot.productionSpeed - newProductionSpeed) > 0.001) {
        updates.productionSpeed = newProductionSpeed;
        needsUpdate = true;
      }
      
      // 重新计算升级费用
      const newUpgradeCost = plot.isUnlocked ? 
        calculateUpgradeCost(plot.level) : calculateUpgradeCost(1);
      
      if (Math.abs(plot.upgradeCost - newUpgradeCost) > 0.001) {
        updates.upgradeCost = newUpgradeCost;
        needsUpdate = true;
      }
      
      // 重新计算解锁费用
      const newUnlockCost = calculateUnlockCost(plot.plotNumber);
      
      if (Math.abs(plot.unlockCost - newUnlockCost) > 0.001) {
        updates.unlockCost = newUnlockCost;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await queryInterface.sequelize.query(
          `UPDATE farm_plots SET 
             ${Object.keys(updates).map(key => `${key} = :${key}`).join(', ')}
           WHERE id = :id`,
          {
            replacements: { ...updates, id: plot.id },
            type: Sequelize.QueryTypes.UPDATE
          }
        );
        
        console.log(`更新农场区 ${plot.plotNumber} (钱包ID: ${plot.walletId}): ${Object.keys(updates).join(', ')}`);
      }
    }
    
    console.log('农场区块计算公式修复完成');
  },

  async down(queryInterface, Sequelize) {
    console.log('回滚农场区块计算公式修复...');
    
    // 获取所有农场区块数据
    const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    // 辅助函数：格式化为3位小数
    function formatToThreeDecimals(value) {
      return parseFloat(value.toFixed(3));
    }
    
    // 回滚到之前的计算公式
    for (const plot of farmPlots) {
      let updates = {};
      
      // 回滚产量计算（不考虑编号倍数）
      const oldMilkProduction = plot.isUnlocked ? 
        formatToThreeDecimals(Math.pow(1.5, plot.level - 1)) : 0;
      
      // 回滚生产速度计算
      const oldProductionSpeed = formatToThreeDecimals(5.0 / Math.pow(1.05, plot.level - 1));
      
      // 回滚升级费用计算
      const oldUpgradeCost = formatToThreeDecimals(200 * Math.pow(1.5, plot.level - 1));
      
      updates.milkProduction = oldMilkProduction;
      updates.productionSpeed = oldProductionSpeed;
      updates.upgradeCost = oldUpgradeCost;
      
      await queryInterface.sequelize.query(
        `UPDATE farm_plots SET 
           milkProduction = :milkProduction,
           productionSpeed = :productionSpeed,
           upgradeCost = :upgradeCost
         WHERE id = :id`,
        {
          replacements: { ...updates, id: plot.id },
          type: Sequelize.QueryTypes.UPDATE
        }
      );
    }
    
    console.log('农场区块计算公式回滚完成');
  }
};
