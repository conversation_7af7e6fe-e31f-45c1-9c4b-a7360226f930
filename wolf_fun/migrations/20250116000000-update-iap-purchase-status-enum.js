'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  /**
   * 更新 iap_purchases 表的 status 字段，添加新的 Dapp Portal 状态
   */
  async up(queryInterface, Sequelize) {
    
    // 步骤1: 添加临时的新状态列
    await queryInterface.addColumn('iap_purchases', 'status_new', {
      type: Sequelize.ENUM(
        'CREATED',
        'STARTED', 
        'REGISTERED_ON_PG',
        'CAPTURED',
        'PENDING',
        'CONFIRMED',
        'FINALIZED',
        'REFUNDED',
        'CONFIRM_FAILED',
        'CANCELED',
        'CHARGEBACK'
      ),
      allowNull: true
    });

    // 步骤2: 通过 Dapp Portal API 获取真实状态并更新
    const purchases = await queryInterface.sequelize.query(
      "SELECT id, paymentId FROM iap_purchases WHERE paymentId IS NOT NULL",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // 批量更新状态
    for (const purchase of purchases) {
      try {
        // 调用 Dapp Portal API 获取支付状态
        const response = await fetch(`https://payment.dappportal.io/api/payment-v1/payment/status?id=${purchase.paymentId}`);
        
        if (response.ok) {
          const statusData = await response.json();
          const realStatus = statusData.status;
          
          // 根据 API 返回的状态更新数据库
          if (realStatus) {
            await queryInterface.sequelize.query(
              "UPDATE iap_purchases SET status_new = ? WHERE id = ?",
              {
                replacements: [realStatus, purchase.id],
                type: queryInterface.sequelize.QueryTypes.UPDATE
              }
            );
          }
        } else {
          console.log(`Failed to get status for payment ${purchase.paymentId}, keeping original status`);
        }
      } catch (error) {
        console.log(`Error fetching status for payment ${purchase.paymentId}:`, error.message);
      }
    }
    // 步骤3: 删除旧的 status 列
    await queryInterface.removeColumn('iap_purchases', 'status');

    // 步骤4: 重命名新列为 status
    await queryInterface.renameColumn('iap_purchases', 'status_new', 'status');

    // 步骤5: 修改列属性，设置为不允许为空并设置默认值
    await queryInterface.changeColumn('iap_purchases', 'status', {
      type: Sequelize.ENUM(
        'CREATED',
        'STARTED', 
        'REGISTERED_ON_PG',
        'CAPTURED',
        'PENDING',
        'CONFIRMED',
        'FINALIZED',
        'REFUNDED',
        'CONFIRM_FAILED',
        'CANCELED',
        'CHARGEBACK'
      ),
      allowNull: false,
      defaultValue: 'CREATED'
    });
  },

  /**
   * 回滚操作：恢复到原来的状态值
   */
  async down(queryInterface, Sequelize) {
    // 回滚到原来的 ENUM 值
    await queryInterface.changeColumn('iap_purchases', 'status', {
      type: Sequelize.ENUM(
        'PENDING',
        'CONFIRMED',
        'FINALIZED',
        'REFUNDED',
        'CONFIRM_FAILED',
        'CANCELED',
        'CHARGEBACK'
      ),
      allowNull: false,
      defaultValue: 'PENDING'
    });
  }
};