const { Sequelize } = require('sequelize');
require('dotenv').config();

async function syncDatabaseForce() {
  console.log('⚠️  开始强制同步数据表到数据库（将删除现有数据）...');
  console.log('⚠️  此操作将删除所有现有数据，请确认！');
  
  // 添加确认提示
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const answer = await new Promise(resolve => {
    rl.question('确认要强制重建所有表吗？这将删除所有数据！(yes/no): ', resolve);
  });
  
  rl.close();
  
  if (answer.toLowerCase() !== 'yes') {
    console.log('❌ 操作已取消');
    return;
  }
  
  try {
    // 导入模型和 sequelize 实例
    console.log('📝 导入模型定义...');
    const db = require('./models');
    const sequelize = db.sequelize;
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    console.log('📊 发现以下模型:');
    Object.keys(db).forEach(modelName => {
      if (modelName !== 'sequelize' && modelName !== 'Sequelize') {
        console.log(`   - ${modelName}`);
      }
    });

    // 强制同步所有模型到数据库
    console.log('\n🔄 开始强制重建数据表...');
    
    await sequelize.sync({ 
      force: true,
      logging: (sql) => {
        console.log('📝 执行SQL:', sql);
      }
    });
    
    console.log('\n🎉 数据表强制同步完成！');
    console.log('✅ 所有模型已成功重建到数据库');
    
  } catch (error) {
    console.error('❌ 同步失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行同步
if (require.main === module) {
  syncDatabaseForce();
}

module.exports = { syncDatabaseForce };
