// src/models/index.ts
import { User } from "./User";
import { Chest } from "./Chest";
import { Reward } from "./Reward";

import { Reservation } from "./Reservation";
import { Room } from "./Room";
import { Session } from "./Session";
import { Round } from "./Round";

import { UserDailyClaim } from "./UserDailyClaim";

import { UserWallet } from "./UserWallet";

import { GameHistory } from "./GameHistory";
import { WalletHistory } from "./WalletHistory";
import { Withdrawal } from "./Withdrawal";

import { Tasks } from "./Tasks";
import { UserTaskComplete } from "./UserTaskComplete";

import { PrizePool } from "./PrizePool";

import { BullUnlockHistory } from "./BullUnlockHistory";

import { UserStatusSnapshot } from "./UserStatusSnapshot";

import { BullKingRecord } from "./BullKingRecord";


import { Announcement } from "./Announcement";

import { RewardClaim } from "./RewardClaim";

import { PendingRebate } from "./PendingRebate";


import { MissedRebate } from "./MissedRebate";

import { RebateBackup } from "./RebateBackup";

import { AccountSubscriptionState } from './AccountSubscriptionState';


import { UsdtDepositHistory } from './UsdtDepositHistory';

import { MonitorWalletAddress } from './MonitorWalletAddress';

import { JettonConfig } from './JettonConfig';

import { JackpotPool } from "./JackpotPool";
import { ChestCountdown } from "./ChestCountdown";
import { ChestBoost } from "./ChestBoost";
import { ShareBoostLink } from "./ShareBoostLink";
import { UserChestAcceleration } from "./UserChestAcceleration";

import { PaymentRequest } from "./PaymentRequest";
import { PurchaseRecord } from "./PurchaseRecord";

// 新增游戏相关模型导入
import { FarmPlot } from "./FarmPlot";
import { DeliveryLine } from "./DeliveryLine";

// 新增IAP相关模型导入
import { Booster } from "./Booster";
import { ActiveBooster } from "./ActiveBooster";
import { IapProduct } from "./IapProduct";
import { IapPurchase } from "./IapPurchase";
import { VipMembership } from "./VipMembership";
import { TimeWarpHistory } from "./TimeWarpHistory";

// 这里可以设置关联关系，比如 User 一对多 Chest:
User.hasMany(Chest, {
  foreignKey: "userId",
});
Chest.belongsTo(User, {
  foreignKey: "userId",
});

UserWallet.hasMany(Chest, {
  foreignKey: "walletId",
});
Chest.belongsTo(UserWallet, {
  foreignKey: "walletId",
});

// User 和 Reward
User.hasMany(Reward, { foreignKey: "userId" });
Reward.belongsTo(User, { foreignKey: "userId" });

User.hasMany(Reservation, { foreignKey: "userId" });
Reservation.belongsTo(User, { foreignKey: "userId" });


UserWallet.hasMany(Reservation, { foreignKey: "walletId" });
Reservation.belongsTo(UserWallet, { foreignKey: "walletId" });


Session.hasMany(Reservation, { foreignKey: "sessionId" });
Reservation.belongsTo(Session, { foreignKey: "sessionId" });

Room.hasMany(Reservation, { foreignKey: "roomId" });
Reservation.belongsTo(Room, { foreignKey: "roomId" });

// 建立关联关系：一个 Session 可对应多个 Round
Session.hasMany(Round, { foreignKey: "sessionId" });
Round.belongsTo(Session, { foreignKey: "sessionId" });

// 建立关联关系：一个 Session 可对应多个 Room
Session.hasMany(Room, { foreignKey: "sessionId" });
Room.belongsTo(Session, { foreignKey: "sessionId" });

// 添加 Reservation 和 Round 的关联关系
Reservation.belongsTo(Round, { 
  foreignKey: "roundIndex",
  targetKey: "roundIndex",
  constraints: false
});

// Session 和 GameHistory 的关联
Session.hasMany(GameHistory, { foreignKey: "sessionId" });
GameHistory.belongsTo(Session, { foreignKey: "sessionId" });

// 添加 User 和 UserWallet 的关联关系
User.hasMany(UserWallet, { foreignKey: "userId" });
UserWallet.belongsTo(User, { foreignKey: "userId" });

// 添加自引用关系
UserWallet.belongsTo(UserWallet, { as: 'referrer', foreignKey: 'referrerWalletId' });
UserWallet.hasMany(UserWallet, { as: 'referrals', foreignKey: 'referrerWalletId' });

// 添加 PendingRebate 和 User 的关联关系
PendingRebate.belongsTo(User, { as: 'sourceUser', foreignKey: 'sourceUserId' });
User.hasMany(PendingRebate, { as: 'contributedRebates', foreignKey: 'sourceUserId' });

// 添加 PendingRebate 和 UserWallet 的关联关系
PendingRebate.belongsTo(UserWallet, { foreignKey: 'walletId' });
UserWallet.hasMany(PendingRebate, { foreignKey: 'walletId' });

// 添加 PendingRebate 和 User (接收者) 的关联关系
PendingRebate.belongsTo(User, { as: 'user', foreignKey: 'userId' });
User.hasMany(PendingRebate, { as: 'receivedRebates', foreignKey: 'userId' });

// 添加新的关联关系
User.hasOne(ChestCountdown, { foreignKey: "userId" });
ChestCountdown.belongsTo(User, { foreignKey: "userId" });

UserWallet.hasOne(ChestCountdown, { foreignKey: "walletId" });
ChestCountdown.belongsTo(UserWallet, { foreignKey: "walletId" });

// 用户宝箱加速记录关联
User.hasMany(UserChestAcceleration, { foreignKey: "userId" });
UserChestAcceleration.belongsTo(User, { foreignKey: "userId" });

UserWallet.hasMany(UserChestAcceleration, { foreignKey: "walletId" });
UserChestAcceleration.belongsTo(UserWallet, { foreignKey: "walletId" });

User.hasMany(ChestBoost, { as: 'sourceBoosts', foreignKey: "sourceUserId" });
ChestBoost.belongsTo(User, { as: 'sourceUser', foreignKey: "sourceUserId" });

User.hasMany(ChestBoost, { as: 'targetBoosts', foreignKey: "targetUserId" });
ChestBoost.belongsTo(User, { as: 'targetUser', foreignKey: "targetUserId" });

User.hasMany(ShareBoostLink, { foreignKey: "userId" });
ShareBoostLink.belongsTo(User, { foreignKey: "userId" });

Chest.hasOne(ShareBoostLink, { foreignKey: "chestId" });
ShareBoostLink.belongsTo(Chest, { foreignKey: "chestId" });

// 新增IAP相关模型的关联关系

// Booster 关联关系
UserWallet.hasMany(Booster, { foreignKey: 'walletId' });
Booster.belongsTo(UserWallet, { foreignKey: 'walletId' });

// ActiveBooster 关联关系
UserWallet.hasMany(ActiveBooster, { foreignKey: 'walletId' });
ActiveBooster.belongsTo(UserWallet, { foreignKey: 'walletId' });
ActiveBooster.belongsTo(IapProduct, { foreignKey: 'productId', as: 'product' });

// IapPurchase 关联关系
UserWallet.hasMany(IapPurchase, { foreignKey: 'walletId' });
IapPurchase.belongsTo(UserWallet, { foreignKey: 'walletId' });

IapProduct.hasMany(IapPurchase, { foreignKey: 'productId' });
IapPurchase.belongsTo(IapProduct, { foreignKey: 'productId' });

// VipMembership 关联关系
UserWallet.hasOne(VipMembership, { foreignKey: 'walletId' });
VipMembership.belongsTo(UserWallet, { foreignKey: 'walletId' });

// FarmPlot 关联关系
UserWallet.hasMany(FarmPlot, { foreignKey: 'walletId' });
FarmPlot.belongsTo(UserWallet, { foreignKey: 'walletId' });

// DeliveryLine 关联关系
UserWallet.hasOne(DeliveryLine, { foreignKey: 'walletId' });
DeliveryLine.belongsTo(UserWallet, { foreignKey: 'walletId' });

// TimeWarpHistory 关联关系
UserWallet.hasMany(TimeWarpHistory, { foreignKey: 'walletId' });
TimeWarpHistory.belongsTo(UserWallet, { foreignKey: 'walletId' });

IapProduct.hasMany(TimeWarpHistory, { foreignKey: 'productId' });
TimeWarpHistory.belongsTo(IapProduct, { foreignKey: 'productId' });

IapPurchase.hasMany(TimeWarpHistory, { foreignKey: 'purchaseId' });
TimeWarpHistory.belongsTo(IapPurchase, { foreignKey: 'purchaseId' });

export {
  User,
  Chest,
  Reward,
  UserDailyClaim,
  UserWallet,
  Tasks,
  UserTaskComplete,
  Room,
  Session,
  Round,
  Reservation,
  GameHistory,
  WalletHistory,
  PrizePool,
  BullUnlockHistory,
  BullKingRecord,
  UserStatusSnapshot,
  Announcement,
  RewardClaim,
  PendingRebate,
  MissedRebate,
  RebateBackup,
  AccountSubscriptionState,
  UsdtDepositHistory,
  MonitorWalletAddress,
  JettonConfig,
  JackpotPool,
  ChestCountdown,
  ChestBoost,
  ShareBoostLink,
  PaymentRequest,
  PurchaseRecord, 
  Withdrawal,
  UserChestAcceleration,
  // 新增游戏相关模型导出
  FarmPlot,
  DeliveryLine,
  // 新增IAP相关模型导出
  Booster,
  ActiveBooster,
  IapProduct,
  IapPurchase,
  VipMembership,
  TimeWarpHistory
};
