// 农场区块新设定配置文件
// 基于 doc/农场新设定/ 目录下的配置数据

/**
 * 牛舍数量配置 - 每个等级对应的牛舍数量
 * 索引0对应等级1，索引1对应等级2，以此类推
 */
export const FARM_PLOT_BARN_COUNT = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20];

/**
 * 生产速度配置 - 所有农场区块共享的生产速度数组
 * 索引0对应等级1，索引1对应等级2，以此类推
 * 单位：秒/次
 */
export const FARM_PLOT_PRODUCTION_SPEED = [5,4.75,4.51,4.28,4.07,3.86,3.67,3.49,3.31,3.15,2.99,2.84,2.7,2.56,2.43,2.31,2.2,2.09,1.98,1.88];

/**
 * 解锁费用配置 - 每个农场区块的解锁费用
 * 索引0对应农场区块1，索引1对应农场区块2，以此类推
 */
export const FARM_PLOT_UNLOCK_COST = [0, 378250,1418625,3405000,13241250,7566500,15322500,26484000,62426250,30645750,55333125,87399000,170257500,78697000,135072000,204312000,359437500,160801250,268160625,395385000];

/**
 * 牛奶产量配置 - 每个农场区块在每个等级的牛奶产量
 * 第一维度：农场区块编号(1-20)
 * 第二维度：等级(1-20)
 */
export const FARM_PLOT_MILK_PRODUCTION: number[][] = [
  // 农场区块1
  [1.0, 1.0, 1.0, 1.0, 1.1, 1.1, 1.1, 1.1, 1.2, 1.2, 1.2, 1.2, 1.3, 1.3, 1.3, 1.3, 1.4, 1.4, 1.4, 1.4],
  // 农场区块2
  [1.5, 1.5, 1.5, 1.5, 1.65, 1.65, 1.65, 1.65, 1.8, 1.8, 1.8, 1.8, 1.95, 1.95, 1.95, 1.95, 2.1, 2.1, 2.1, 2.1],
  // 农场区块3
  [2.0, 2.0, 2.0, 2.0, 2.2, 2.2, 2.2, 2.2, 2.4, 2.4, 2.4, 2.4, 2.6, 2.6, 2.6, 2.6, 2.8, 2.8, 2.8, 2.8],
  // 农场区块4
  [2.5, 2.5, 2.5, 2.5, 2.75, 2.75, 2.75, 2.75, 3.0, 3.0, 3.0, 3.0, 3.25, 3.25, 3.25, 3.25, 3.5, 3.5, 3.5, 3.5],
  // 农场区块5
  [3.0, 3.0, 3.0, 3.0, 3.3, 3.3, 3.3, 3.3, 3.6, 3.6, 3.6, 3.6, 3.9, 3.9, 3.9, 3.9, 4.2, 4.2, 4.2, 4.2],
  // 农场区块6
  [3.5, 3.5, 3.5, 3.5, 3.85, 3.85, 3.85, 3.85, 4.2, 4.2, 4.2, 4.2, 4.55, 4.55, 4.55, 4.55, 4.9, 4.9, 4.9, 4.9],
  // 农场区块7
  [4.0, 4.0, 4.0, 4.0, 4.4, 4.4, 4.4, 4.4, 4.8, 4.8, 4.8, 4.8, 5.2, 5.2, 5.2, 5.2, 5.6, 5.6, 5.6, 5.6],
  // 农场区块8
  [4.5, 4.5, 4.5, 4.5, 4.95, 4.95, 4.95, 4.95, 5.4, 5.4, 5.4, 5.4, 5.85, 5.85, 5.85, 5.85, 6.3, 6.3, 6.3, 6.3],
  // 农场区块9
  [5.0, 5.0, 5.0, 5.0, 5.5, 5.5, 5.5, 5.5, 6.0, 6.0, 6.0, 6.0, 6.5, 6.5, 6.5, 6.5, 7.0, 7.0, 7.0, 7.0],
  // 农场区块10
  [5.5, 5.5, 5.5, 5.5, 6.05, 6.05, 6.05, 6.05, 6.6, 6.6, 6.6, 6.6, 7.15, 7.15, 7.15, 7.15, 7.7, 7.7, 7.7, 7.7],
  // 农场区块11
  [6.0, 6.0, 6.0, 6.0, 6.6, 6.6, 6.6, 6.6, 7.2, 7.2, 7.2, 7.2, 7.8, 7.8, 7.8, 7.8, 8.4, 8.4, 8.4, 8.4],
  // 农场区块12
  [6.5, 6.5, 6.5, 6.5, 7.15, 7.15, 7.15, 7.15, 7.8, 7.8, 7.8, 7.8, 8.45, 8.45, 8.45, 8.45, 9.1, 9.1, 9.1, 9.1],
  // 农场区块13
  [7.0, 7.0, 7.0, 7.0, 7.7, 7.7, 7.7, 7.7, 8.4, 8.4, 8.4, 8.4, 9.1, 9.1, 9.1, 9.1, 9.8, 9.8, 9.8, 9.8],
  // 农场区块14
  [7.5, 7.5, 7.5, 7.5, 8.25, 8.25, 8.25, 8.25, 9.0, 9.0, 9.0, 9.0, 9.75, 9.75, 9.75, 9.75, 10.5, 10.5, 10.5, 10.5],
  // 农场区块15
  [8.0, 8.0, 8.0, 8.0, 8.8, 8.8, 8.8, 8.8, 9.6, 9.6, 9.6, 9.6, 10.4, 10.4, 10.4, 10.4, 11.2, 11.2, 11.2, 11.2],
  // 农场区块16
  [8.5, 8.5, 8.5, 8.5, 9.35, 9.35, 9.35, 9.35, 10.2, 10.2, 10.2, 10.2, 11.05, 11.05, 11.05, 11.05, 11.9, 11.9, 11.9, 11.9],
  // 农场区块17
  [9.0, 9.0, 9.0, 9.0, 9.9, 9.9, 9.9, 9.9, 10.8, 10.8, 10.8, 10.8, 11.7, 11.7, 11.7, 11.7, 12.6, 12.6, 12.6, 12.6],
  // 农场区块18
  [9.5, 9.5, 9.5, 9.5, 10.45, 10.45, 10.45, 10.45, 11.4, 11.4, 11.4, 11.4, 12.35, 12.35, 12.35, 12.35, 13.3, 13.3, 13.3, 13.3],
  // 农场区块19
  [10.0, 10.0, 10.0, 10.0, 11.0, 11.0, 11.0, 11.0, 12.0, 12.0, 12.0, 12.0, 13.0, 13.0, 13.0, 13.0, 14.0, 14.0, 14.0, 14.0],
  // 农场区块20
  [10.5, 10.5, 10.5, 10.5, 11.55, 11.55, 11.55, 11.55, 12.6, 12.6, 12.6, 12.6, 13.65, 13.65, 13.65, 13.65, 14.7, 14.7, 14.7, 14.7]
];

/**
 * 升级费用配置 - 每个农场区块在每个等级的升级费用
 * 第一维度：农场区块编号(1-20)
 * 第二维度：等级(1-20)
 */
export const FARM_PLOT_UPGRADE_COST: number[][] = [
  // 农场区块1
  [100, 315, 660, 1743, 1012, 1912, 3135, 6300, 3260, 5700, 8820, 15843, 7812, 13312, 20050, 33750, 16215, 27112, 40290, 65143],
  // 农场区块2
  [9493, 14550, 19850, 38179, 15853, 24539, 33806, 58343, 24725, 38625, 53775, 87851, 37921, 59882, 84125, 133078, 58331, 92728, 131400, 203601],
  // 农场区块3
  [28222, 42828, 57840, 109940, 44921, 68580, 93195, 158512, 65625, 100890, 138150, 222046, 93262, 144675, 199762, 310668, 132435, 207022, 288405, 439753],
  // 农场区块4
  [59071, 89328, 120172, 227521, 92373, 140332, 189656, 320731, 131530, 200891, 273140, 435750, 180796, 278184, 380931, 587475, 246881, 382528, 528097, 798087],
  // 农场区块5
  [104840, 158250, 212460, 401400, 162435, 246060, 331590, 559000, 228040, 346980, 469920, 746562, 307525, 470850, 641550, 984375, 410070, 631845, 867240, 1303050],
  // 农场区块6
  [168322, 253783, 340290, 642051, 259267, 392090, 527377, 887287, 360742, 547593, 739665, 1171828, 480431, 733218, 995625, 1522378, 630382, 967545, 1322595, 0],
  // 农场区块7
  [252300, 380100, 509250, 959859, 387075, 584634, 785362, 1319500, 535225, 811012, 1093450, 1729062, 706468, 1075640, 1457062, 2222343, 916162, 1402087, 1910775, 2850421],
  // 农场区块8
  [359576, 541406, 724900, 1365426, 550048, 830053, 1113956, 1869656, 757047, 1145718, 1542585, 2435640, 992646, 1508718, 2039950, 3105300, 1275821, 1948196, 2648745, 3941798],
  // 农场区块9
  [492945, 741892, 992850, 1869187, 752355, 1134573, 1521540, 2551725, 1031820, 1560015, 2098200, 3309187, 1345950, 2042831, 2758125, 4192312, 1717740, 2618392, 3553200, 5277431],
  // 农场区块10
  [655183, 985725, 1318655, 2481557, 998205, 1504510, 2016397, 3379593, 1365097, 2062320, 2771405, 4366882, 1773321, 2688501, 3625537, 5503996, 2250251, 3425175, 4640707, 6881875],
  // 农场区块11
  [849100, 1277115, 1707965, 3213000, 1291762, 1946109, 2607045, 4367300, 1762495, 2660962, 3573360, 5626468, 2281781, 3456140, 4656225, 7061512, 2881725, 4381177, 5928300, 8779575],
  // 农场区块12
  [1077487, 1620253, 2166300, 4074117, 1637268, 2465732, 3301818, 5528812, 2229600, 3364425, 4515375, 7105195, 2878312, 4356351, 5864250, 8885812, 3620643, 5498971, 7432650, 10994812],
  // 农场区块13
  [1343120, 2019300, 2699240, 5075250, 2038830, 3069540, 4109040, 6878100, 2771960, 4180920, 5608480, 8820625, 3569850, 5399400, 7263300, 10997850, 4475280, 6791040, 9170400, 13552175],
  // 农场区块14
  [1648808, 2478472, 3312407, 6226860, 2500721, 3763943, 5037142, 8429025, 3395240, 5118997, 6863920, 10790085, 4363421, 6595973, 8867518, 13418418, 5454067, 8270032, 11158290, 16476107],
  // 农场区块15
  [1997347, 3001961, 4011435, 7539581, 3027105, 4555136, 6094507, 10195650, 4104945, 6186915, 8292780, 13031296, 5266012, 7956393, 10690875, 16168612, 6565387, 9948420, 13413330, 19790859],
  // 农场区块16
  [2391506, 3593921, 4801775, 9023723, 3622100, 5449449, 7289445, 12191825, 4906655, 7393113, 9906315, 15561296, 6284546, 9491123, 12747218, 19269206, 7817550, 11838864, 15952020, 23521050],
  // 农场区块17
  [2834100, 4258575, 5689100, 10689843, 4290000, 6453112, 8630400, 14431625, 5806000, 8745900, 11715700, 18397968, 7426062, 11210812, 15050750, 22741312, 9219000, 13953937, 18791100, 27691125],
  // 农场区块18
  [3327922, 5000152, 6678997, 12548320, 5034920, 7572521, 10125753, 16929018, 6808567, 10253801, 13732005, 21558468, 8697543, 13125754, 17615193, 26606081, 10778118, 16306211, 21947625, 32325300],
  // 农场区块19
  [3875740, 5822726, 7777055, 14609718, 5861088, 8813784, 11783805, 19697837, 7919890, 11924962, 15966500, 25060406, 10105906, 15246515, 20454500, 30883875, 12503205, 18908133, 25438050, 37448228],
  // 农场区块20
  [4480371, 6730561, 8988802, 16884515, 6772651, 10183365, 13612923, 22752175, 9145605, 13768001, 18430360, 28921062, 11658196, 17583464, 23582762, 35596021, 14402715, 21772389, 29279287, 43084354]
];

/**
 * 获取农场区块的牛舍数量
 * @param level 等级 (1-20)
 * @returns 牛舍数量
 */
export function getFarmPlotBarnCount(level: number): number {
  if (level < 1 || level > 20) {
    throw new Error(`Invalid level: ${level}. Level must be between 1 and 20.`);
  }
  return FARM_PLOT_BARN_COUNT[level - 1];
}

/**
 * 获取农场区块的生产速度
 * @param level 等级 (1-20)
 * @returns 生产速度（秒/次）
 */
export function getFarmPlotProductionSpeed(level: number): number {
  if (level < 1 || level > 20) {
    throw new Error(`Invalid level: ${level}. Level must be between 1 and 20.`);
  }
  return FARM_PLOT_PRODUCTION_SPEED[level - 1];
}

/**
 * 获取农场区块的解锁费用
 * @param plotNumber 农场区块编号 (1-20)
 * @returns 解锁费用
 */
export function getFarmPlotUnlockCost(plotNumber: number): number {
  if (plotNumber < 1 || plotNumber > 20) {
    throw new Error(`Invalid plotNumber: ${plotNumber}. PlotNumber must be between 1 and 20.`);
  }
  return FARM_PLOT_UNLOCK_COST[plotNumber - 1];
}

/**
 * 获取农场区块的牛奶产量
 * @param plotNumber 农场区块编号 (1-20)
 * @param level 等级 (1-20)
 * @returns 牛奶产量
 */
export function getFarmPlotMilkProduction(plotNumber: number, level: number): number {
  if (plotNumber < 1 || plotNumber > 20) {
    throw new Error(`Invalid plotNumber: ${plotNumber}. PlotNumber must be between 1 and 20.`);
  }
  if (level < 1 || level > 20) {
    throw new Error(`Invalid level: ${level}. Level must be between 1 and 20.`);
  }
  return FARM_PLOT_MILK_PRODUCTION[plotNumber - 1][level - 1];
}

/**
 * 获取农场区块的升级费用
 * @param plotNumber 农场区块编号 (1-20)
 * @param level 等级 (1-20)
 * @returns 升级费用
 */
export function getFarmPlotUpgradeCost(plotNumber: number, level: number): number {
  if (plotNumber < 1 || plotNumber > 20) {
    throw new Error(`Invalid plotNumber: ${plotNumber}. PlotNumber must be between 1 and 20.`);
  }
  if (level < 1 || level > 20) {
    throw new Error(`Invalid level: ${level}. Level must be between 1 and 20.`);
  }
  return FARM_PLOT_UPGRADE_COST[plotNumber - 1][level - 1];
}
