// test_excel_api.js - 测试Excel上传API
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// 配置
const API_BASE_URL = 'http://localhost:3456';
const EXCEL_FILE_PATH = path.join(__dirname, 'doc/区域升级.xlsx');

// 模拟JWT token（在实际使用中需要真实的token）
const MOCK_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************.test_signature';

async function testExcelUploadAPI() {
  console.log('🚀 开始测试Excel上传API...');
  console.log('📍 API地址:', API_BASE_URL);
  console.log('📁 Excel文件:', EXCEL_FILE_PATH);

  try {
    // 1. 测试健康检查
    console.log('\n1️⃣ 测试服务器健康状态...');
    try {
      const healthResponse = await axios.get(`${API_BASE_URL}/api/health/check`);
      console.log('✅ 服务器健康状态:', healthResponse.data);
    } catch (error) {
      console.log('⚠️ 健康检查失败，但继续测试...');
    }

    // 2. 测试获取Excel模板
    console.log('\n2️⃣ 测试获取Excel模板...');
    try {
      const templateResponse = await axios.get(`${API_BASE_URL}/api/excel/template`, {
        headers: {
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`
        },
        responseType: 'arraybuffer'
      });
      
      if (templateResponse.status === 200) {
        console.log('✅ Excel模板获取成功');
        console.log('   文件大小:', templateResponse.data.length, 'bytes');
        
        // 保存模板文件
        const templatePath = path.join(__dirname, 'downloaded_template.xlsx');
        fs.writeFileSync(templatePath, templateResponse.data);
        console.log('   模板已保存到:', templatePath);
      }
    } catch (error) {
      console.log('❌ 获取Excel模板失败:', error.response?.data || error.message);
    }

    // 3. 测试Excel文件上传
    console.log('\n3️⃣ 测试Excel文件上传...');
    
    if (!fs.existsSync(EXCEL_FILE_PATH)) {
      console.log('❌ Excel文件不存在:', EXCEL_FILE_PATH);
      return;
    }

    const formData = new FormData();
    formData.append('excelFile', fs.createReadStream(EXCEL_FILE_PATH));

    try {
      const uploadResponse = await axios.post(`${API_BASE_URL}/api/excel/upload`, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      });

      if (uploadResponse.status === 200) {
        console.log('✅ Excel文件上传成功');
        console.log('   响应数据:');
        console.log('   - 文件名:', uploadResponse.data.data.fileName);
        console.log('   - 文件大小:', uploadResponse.data.data.fileSize, 'bytes');
        console.log('   - 工作表数量:', uploadResponse.data.data.sheetNames.length);
        console.log('   - 工作表名称:', uploadResponse.data.data.sheetNames);
        console.log('   - 解析的区域数量:', uploadResponse.data.data.parsedData.summary.totalRegions);
        
        // 显示解析后的数据示例
        if (uploadResponse.data.data.parsedData.regions.length > 0) {
          const firstRegion = uploadResponse.data.data.parsedData.regions[0];
          console.log('   - 第一个区域标题:', firstRegion.headers);
          console.log('   - 第一个区域数据行数:', firstRegion.totalRows);
          
          if (firstRegion.data.length > 0) {
            console.log('   - 第一行数据示例:', firstRegion.data[0]);
          }
        }
      }
    } catch (error) {
      console.log('❌ Excel文件上传失败:', error.response?.data || error.message);
      if (error.response?.status === 401) {
        console.log('   提示: 可能需要有效的JWT token进行认证');
      }
    }

    // 4. 测试批量处理功能
    console.log('\n4️⃣ 测试批量处理功能...');
    
    const mockUpgradeData = [
      {
        regionId: '1',
        regionName: '农场区域1',
        currentLevel: '1',
        upgradeCost: '100',
        effect: '产量+20%'
      },
      {
        regionId: '2',
        regionName: '农场区域2',
        currentLevel: '2',
        upgradeCost: '200',
        effect: '产量+25%'
      }
    ];

    try {
      const batchResponse = await axios.post(`${API_BASE_URL}/api/excel/batch-upgrade`, {
        upgradeData: mockUpgradeData
      }, {
        headers: {
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      if (batchResponse.status === 200) {
        console.log('✅ 批量处理成功');
        console.log('   处理数量:', batchResponse.data.data.processedCount);
        console.log('   处理结果:', batchResponse.data.data.results);
      }
    } catch (error) {
      console.log('❌ 批量处理失败:', error.response?.data || error.message);
    }

    console.log('\n🎉 Excel API测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testExcelUploadAPI();
}

module.exports = { testExcelUploadAPI };
